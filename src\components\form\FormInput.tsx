import { useFormContext } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

interface FormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'prefix' | 'suffix'> {
  name: string
  label?: string
  onChange?: (newValue: string) => void
  suffix?: React.ReactNode
  prefix?: React.ReactNode
  containerClassName?: string
}

export function FormInput({ name, label, onChange, suffix, prefix, containerClassName, ...props }: FormInputProps) {
  const { control } = useFormContext()

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && (
            <FormLabel>
              {label} {props.required && <span className="text-error-700">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <Input
              {...field}
              onChange={(newValue) => {
                field.onChange(newValue)
                onChange && onChange(newValue.target.value)
              }}
              containerClassName={containerClassName}
              suffix={suffix}
              prefix={prefix}
              {...props}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
