import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FormField, FormItem } from '../../ui/form'
import { File, X } from 'lucide-react'
import Image from 'next/image'
import useFormFileUpload, { FormFileUploadProps } from './useFormFileUpload'

export interface IFormImageUploadProps extends FormFileUploadProps {
  children?: React.ReactNode
}

interface FilesPreviewProps {
  selectedFiles: (File | IFileResponse)[] | File | IFileResponse
  removeImage: (index: number, url?: string) => void
}

const FormImageUpload = ({
  name,
  multiple,
  maxSize = 10,
  maxLength,
  accept = 'image/*',
  children,
}: IFormImageUploadProps) => {
  const {
    selectedFiles,
    handleFileChange,
    removeImage,
    validateMaxLength,
    validateMaxSize,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    getAllowedFileTypesDescription,
    isDragging,
    fileInputRef,
    errors,
    control,
    t,
  } = useFormFileUpload({
    name,
    multiple,
    maxSize,
    maxLength,
    accept,
  })
  return (
    <FormField
      name={name}
      control={control}
      render={() => (
        <div
          className={cn(
            isDragging && 'border-primary',
            errors[name] && 'bg-red-50 border-red-300 hover:border-red-400'
          )}
        >
          <FilesPreview selectedFiles={selectedFiles} removeImage={removeImage} />
          <label htmlFor={`image-upload-${name}`} className="flex flex-col gap-2 w-full">
            <FormItem>
              {/* {label && <FormLabel>{label}</FormLabel>} */}
              <div onDrop={handleDrop} onDragOver={handleDragOver} onDragLeave={handleDragLeave}>
                {/* <CloudUpload size={24} />
                    <p className="font-medium text-sm">{t('label.file_upload_placeholder_title')}</p>
                    <p className="text-sm">
                      {t('label.file_upload_file_types', { accept: getAllowedFileTypesDescription() })}
                    </p>
                    <p className="text-sm text-center">
                      {`${t('label.file_upload_max_file_size', { maxSize })} ${maxLength && maxSize && t('label.and')} ${maxLength && t('label.file_upload_max_file_number', { maxLength })}`}
                    </p> */}
                {children || (
                  <span className="bg-bg-action-light dark:bg-bg-action-dark text-primary dark:text-white">
                    <Image src={'/icons/document-upload.svg'} alt="upload" width={24} height={24} />
                    {t('label.upload_profile_image')}
                  </span>
                )}
                <input
                  multiple={multiple}
                  ref={fileInputRef}
                  id={`image-upload-${name}`}
                  type="file"
                  accept={accept}
                  onChange={handleFileChange}
                  className="hidden"
                />
              </div>
            </FormItem>
          </label>
        </div>
      )}
    />
  )
}

export default FormImageUpload

const FilesPreview = ({ selectedFiles, removeImage }: FilesPreviewProps) => {
  if (!selectedFiles) return <></>

  const filesAsArray = Array.isArray(selectedFiles) ? selectedFiles : [selectedFiles]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-2 group">
      {filesAsArray.map((file, index) => {
        const isExistingFile = 'path' in file
        // const fileSize = isExistingFile ? 'N/A' : file.size
        const fileUrl = isExistingFile ? file.path : URL.createObjectURL(file)

        return (
          <div
            key={isExistingFile ? `${file.id}-${file.name}` : file.name}
            className="p-2 border border-input-border-light dark:border-input-border-dark rounded-md relative max-w-[120px]"
          >
            {file.type.startsWith('image/') ? (
              <Image
                alt={file.name}
                src={fileUrl}
                className="object-cover object-center w-full h-full rounded-md"
                width={60}
                height={60}
              />
            ) : (
              <File size={24} />
            )}
            <Button
              type="button"
              variant="destructive"
              onClick={() => removeImage(index, isExistingFile ? undefined : fileUrl)}
              className="absolute top-[-4px] end-[-4px] opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto !p-0 bg-bg-error-dark rounded-full w-6 h-6 flex items-center justify-center"
            >
              <X size={24} />
            </Button>
          </div>
        )
      })}
    </div>
  )
}
