import Image from 'next/image'
import { useLocale } from 'next-intl'
import Logo from '/public/icons/logo.svg'
import RegisterBg from '/public/images/auth-bg.png'

const AuthLayoutComponent = ({
  children,
}: Readonly<{
  children: React.ReactNode
}>) => {
  const isRtl = useLocale() === 'ar'

  return (
    <div
      className={`relative h-screen grid lg:grid-cols-2 grid-cols-1 items-center  ${isRtl ? 'auth-layout-rtl' : 'auth-layout-ltr'}`}
    >
      <figure className="w-full h-full p-5 max-h-screen max-lg:hidden">
        <Image
          src={RegisterBg}
          alt="Auth background"
          className="object-cover -z-10 rounded-[20px] h-full w-full"
          priority
        />
      </figure>

      <main className="relative z-10 min-h-screen flex flex-col gap-4 sm:gap-6 md:gap-8 lg:gap-10 justify-center items-center md:px-14  md:py-12 sm:p-7 p-4  w-full">
        <Image alt="logo" height={120} width={130} src={Logo} />
        {children}
      </main>
    </div>
  )
}

export default AuthLayoutComponent
