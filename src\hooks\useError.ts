'use client'

import { toast } from 'sonner'
import { signOut } from 'next-auth/react'

// Types
import type { IError } from './types'
import { deleteCookie } from 'cookies-next'

type TNotifyType = 'error' | 'success' | 'warning' | 'info'

const useError = () => {
  const handleLogout = () => {
    deleteCookie('fcm_token')
    signOut({ callbackUrl: '/', redirect: true })
  }
  const handleErrors = (error: IError, type: TNotifyType = 'error', fallbackMessage = 'Something went wrong') => {
    // Handle 401 errors by signing out the user
    if (typeof error === 'object' && error !== null && (error as IError).statusCode === 401) {
      handleLogout()
      return
    }

    if (typeof error === 'object' && error !== null) {
      Object.values(error).forEach((msg: any) => {
        if (Array.isArray(msg)) {
          msg.forEach((m) => {
            toast[type](String(m) || fallbackMessage)
          })
        } else {
          toast[type](String(msg) || fallbackMessage)
        }
      })
    } else if (typeof error === 'string') {
      toast[type](error)
    } else {
      toast[type](fallbackMessage)
    }
  }

  return {
    handleErrors,
  }
}

export default useError
