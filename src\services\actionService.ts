'use server'

import { apiService, ApiServiceProps } from './apiService'

export type ActionServiceProps = ApiServiceProps

export async function actionService<T>(props: ActionServiceProps, _: any, body: any): Promise<ActionServiceReturn<T>> {
  const res: any = await apiService({ body, ...props })

  return {
    status: res.status,
    statusCode: res.statusCode,
    ...(res && { data: res }),
    message: res?.message || 'Success',
    error: res.statusCode === 422 ? res.errors : res.message,
  }
}
