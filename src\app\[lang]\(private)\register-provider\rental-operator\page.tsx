import RegisterRentalOperatorPage from '@/modules/register-provider/RegisterRentalOperator'
import { TProfile } from '@/modules/register-provider/types'
import { apiService } from '@/services'
import { Metadata } from 'next'

export const dynamic = 'force-dynamic'
export const metadata: Metadata = {
  title: 'Register As Rental Operator',
  description:
    'Fill in the information below to register as an equipment rental operator and provide equipment to customers.',
}

const Page = async () => {
  const res = apiService({
    path: '/profile',
  })
  const userData: TProfile = res?.data?.data

  return <RegisterRentalOperatorPage userData={userData} />
}

export default Page
